<!--手机端测评报告页面-->
    <template>
        <div class="mobile-report-main">
          <!-- 顶部导航栏 -->
          <div class="mobile-header">
            <div class="header-back" @click="goBack">
              <!-- <svg width="24" height="53" viewBox="0 0 24 24" fill="none">
                <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg> -->
              <img style="width: 10px;height: 17px;" src="@/assets/img/academic/jtfh.png" alt="">
            </div>
            <div class="header-title">测评报告</div>
            <div class="header-placeholder"></div>
            <div class="test-date-tag">
                测评日期: {{reportData?.createTime ||  "2025-06-30 17:50:44"}}
            </div>
          </div>
      
          <!-- 滚动内容区 -->
          <div class="mobile-content" ref="mobileContentRef">
            <!-- 用户信息卡片 -->
            <div class="user-card">
              <div class="user-info">
                <div class="user-avatar">
                  <img :src="reportData.avatar || 'https://xiaoyin-test.obs.cn-south-1.myhuaweicloud.com/education/assets/img/1.0/vip/head-girl.svg'" alt="头像" class="avatar-img" />
                </div>
                <div class="user-details">
                  <div class="user-name">{{ reportData.nickName || '用户' }}<span class="semester">{{reportData?.gradeId}}{{ reportData?.termId == 1 ? '上学期' : '下学期' }}</span></div>
                </div>
              </div>
              
              <!-- 大分数显示 -->
              <!-- <div class="score-display">
                <div class="score-circle">
                  <div class="score-number">{{ formatScore(reportData.correctRate*1) }}</div>
                  <div class="score-unit">分</div>
                </div>
              </div> -->
              <div class="score-fs">
                  <div class="score-number">{{ formatScore(reportData.correctRate*1) }}</div>
                  <div class="score-unit">分</div>
              </div>
              
              <!-- 测评内容 -->
              <div class="test-content">
                <span class="content-label">测评内容:</span>
                <span class="content-text">{{reportData?.title }}</span>
              </div>
              
              <!-- 评级显示 -->
              <div style="color: rgba(50, 58, 87, 1);font-size: 14px;">根据测评得分、测评用时、试题难度等综合评估</div>
              <div class="grade-display">
                <div class="grade-text">当前测评等级为：</div>
                <img class="grade-image" :src="getScoreImages(reportData.correctRate)" alt="评级" />
              </div>
              <img class="not-img" style="margin-top: 30px;" width="360px" height="160px" :src="getScoreImage(reportData.correctRate)" alt="">
            </div>
      
            <!-- 统计卡片 -->
            <div class="stats-card" style="padding-bottom: 14px;">
              <div class="stats-title" style="width: 85px;">
                <span class="title-number">1.</span>
                <span class="title-text">总体情况</span>
              </div>
              
              <div class="stats-grid">
                <!-- 答题数 -->
                <div class="stat-item">
                  <div class="stat-header">
                    <img width="20px" height="20px" src="@/assets/img/entranceAssessment/dtss.png" alt="">
                    <span class="stat-label">答题数: {{ reportData.quesCount }}题</span>
                  </div>
                  <div class="stat-chart" style="margin-left: auto;">
                    <div ref="mobileChart1" class="chart-container"></div>
                  </div>
                  <!-- <div class="stat-value">{{ reportData.quesCount }}题</div> -->
                </div>
                
                <!-- 得分 -->
                <div class="stat-item">
                  <div class="stat-header">
                    <img width="20px" height="20px" src="@/assets/img/entranceAssessment/des.png" alt="">
                    <span class="stat-label">得分: {{ formatScore(reportData.correctRate*1) }}分</span>
                  </div>
                  <div class="stat-chart" style="margin-left: auto;">
                    <div ref="mobileChart2" class="chart-container"></div>
                  </div>
                  <div class="stat-value"></div>
                </div>
              </div>
              
              <!-- 详细统计 -->
              <div class="detailed-stats">
                <div class="stats-row">
                  <div class="stats-item">
                    <img width="20px" height="20px" src="@/assets/img/entranceAssessment/zqls.png" alt="">
                    <span class="stats-label">正确率</span>
                    <span class="stats-number">{{ formatPercentage(reportData.correctRate) }}%</span>
                  </div>
      
                  <div class="stats-circles">
                        <div class="circle-stat">
                            <div class="circle-label">
                                <div class="dot red" style="background: #007fe9;"></div>
                                <span class=" total" style="color: #007fe9;padding-right: 10px;">答题总数</span>
                                <span style="font-size: 16px;">{{ formatScore(reportData?.quesCount*1) }}</span>
                            </div>
                        </div>
                        <div class="circle-stat">
                            <div class="circle-label">
                                <div class="dot red" style="background: #23cb89;"></div>
                                <span class=" correct" style="color: #23cb89;padding-right: 10px;">答对题数</span>
                                <span style="font-size: 16px;">{{ formatScore(reportData?.correct*1) }}</span>   
                            </div>
                        </div>
                        <div class="circle-stat">
                            <div class="circle-label">
                                <div class="dot red" style="background: #ff2055;"></div>
                                <span class=" wrong" style="color: #ff2055;padding-right: 10px;">答错题数</span>
                                <span style="font-size: 16px;">{{ formatScore(reportData?.mistake*1) }}</span>
                            </div>
                        </div>
                    </div>              
                </div>             

              <!-- 答题时长柱状图 -->
              <div class="time-chart-section">
                <div class="time-chart-header">
                  <img width="20px" height="20px" src="@/assets/img/entranceAssessment/dtscs.png" alt="">
                  <span class="time-chart-title">答题时长: {{ formattedTrainTime.value}}{{ formattedTrainTime.unit}}</span>
                </div>
                <div class="time-chart-header" style="padding-top: 10px;">
                  <img width="20px" height="20px" src="@/assets/img/entranceAssessment/dtscs.png" alt="">
                  <span class="time-chart-title">规定答题时长: <span style="">{{ reportData.times ? reportData.times : 25}}分钟</span></span>
                </div>
                <!-- <div class="time-chart-content">
                  <div class="chart-unit">单位: {{ getTimeUnit() }}</div>
                  <div ref="timeChart" class="time-chart-container"></div>
                  
                </div> -->
                <div v-if="isOvertime" class="overtime-warning">
                  <!-- 您超出规定答题时间{{ getOvertimeText() }}，评级在得分基础上下调一级 -->
                  您超出规定答题时间10分钟，评级在得分基础上下调一级
                </div>
              </div>
              </div>
            </div>
      
            <!-- 知识点掌握情况 -->
            <div class="knowledge-card">
              <div class="card-title" style="width: 136px;">
                <span class="title-number">2.</span>
                <span class="title-text">知识点掌握度分析</span>
              </div>
              <div style="border-bottom: 1px solid #f1f1f1;margin: 10px 20px;padding: 10px 0 20px 0;color: rgba(112, 121, 126, 1);font-size: 12px;">
                绿色为强项知识点，黄色为需加强知识点
              </div>
              <div class="top-drop" style="display: flex;font-size: 12px;color: rgba(50, 58, 87, 1);padding: 0 20px 10px 20px;width: 100%;">
                <div>共{{ totalPointsCount }}个</div>  
                <div style="display: flex;justify-content: center;align-items: center;width: 35%;"><span style="width: 13px;height: 13px;background: rgba(35, 203, 137, 1);display: block;margin-right: 5px;"></span>强项知识点：{{ masteredPointsCount }}个</div>
                <div style="display: flex;justify-content: center;align-items: center;width: 35%;"><span style="width: 13px;height: 13px;background: rgba(243, 174, 62, 1);display: block;margin-right: 5px;"></span>需加强知识点：{{ needImprovePointsCount }}个</div>
              </div>
              
              <div class="knowledge-list">
                <div 
                  v-for="(item, index) in reportData?.reportJson?.pointJson" 
                  :key="index"
                  class="knowledge-item"
                >
                  <div class="knowledge-name" :class="{'mastered': item.status === 1, 'need-improve': item.status !== 1}">{{ item.pointName }}</div>
                </div>
              </div>
            </div>
      
            <!-- 薄弱知识点分析 -->
            <div class="weakness-card">
              <div class="card-title" style="width: 123px;">
                <span class="title-number">3.</span>
                <span class="title-text">题目知识点分析</span>
              </div>
              
              <div class="weakness-table">
                <div class="table-header">
                  <div class="header-cell">掌握情况</div>
                  <div class="header-cell">知识点</div>
                  <div class="header-cell">知识点类型</div>
                  <div class="header-cell">分值</div>
                  <div class="header-cell">占比</div>
                </div>
                <div 
                  v-for="(item, index) in reportData?.reportJson?.pointJson" 
                  :key="index"
                  class="table-row"
                >
                  <div class="table-cell">
                    <div class="status-tag" :class="getStatusClass(item.status)">
                      {{ getStatus(item.status) }}
                    </div>
                  </div>
                  <div class="table-cell point-name">{{ item.pointName }}</div>
                  <div class="table-cell">
                    <div class="key-point-type" :class="'type-' + (item.keyPointType || '0')">
                      {{ getKeyPointTypeName(item.keyPointType) }}
                    </div>
                  </div>
                  <div class="table-cell">{{ item.score || '-' }}</div>
                  <div class="table-cell">{{ formatRatio(item.ratio, reportData.ratio) }}{{ item.ratio ? '%' : '' }}</div>
                </div>
              </div>
            </div>
      
            <!-- 学习推荐 -->
            <div class="suggestion-card">
              <div class="card-title" style="width: 86px;">
                <span class="title-number">4.</span>
                <span class="title-text">学习推荐</span>
              </div>

              <div style="color: #333;margin:0 10px; border-bottom: 1px solid #f1f1f1;padding-bottom: 20px;font-size: 12px;">推荐学习以下名师知识点课程，你定能提高不少</div>
              
              <div class="suggestion-content">
                <!-- <div class="suggestion-header">
                  <img style="width: 20px; height: 20px; margin-right: 8px;" src="@/assets/img/entranceAssessment/xx.png" />
                  <img style="width: 80px; height: 20px;" src="@/assets/img/entranceAssessment/xxtj.png" />
                </div> -->
                
                <!-- <div class="suggestion-desc">
                  学习一下面几个知识点再次测评，你的成绩肯定以提高高不少哦。
                </div> -->
                
                <div class="suggestion-tags">
                  <div 
                    v-for="(point, index) in reportData?.reportJson?.pointJson" 
                    :key="index"
                    class="suggestion-tag"
                  >
                  <!-- @click="onGovideo(point)" -->
                    <img style="width: 16px; height: 16px; margin-right: 6px;" src="@/assets/img/entranceAssessment/wkplays.png" />
                    {{ point.pointName }}
                  </div>
                </div>
                
                <!-- <div class="suggestion-buttons">
                  <button class="btn-record" @click="answerRecord()">答题记录</button>
                  <button class="btn-correct" @click="continueStudy">订正错题</button>
                </div> -->
              </div>
            </div>
      
            <!-- 二维码分享 -->
            <!-- <div class="qr-card">
              <div class="qr-title">扫二维码分享报告</div>
              <div class="qr-container" ref="mobileQrContainer">
                <div v-if="screenshotLoading" class="qr-loading">
                  <div class="loading-spinner"></div>
                  <div class="loading-text">生成中...</div>
                </div>
                <canvas v-else ref="mobileQrCanvas" width="120" height="120"></canvas>
              </div>
            </div> -->
          </div>
        </div>
      </template>
      
      <script lang="ts" setup>
      import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue'
      import { useRouter, useRoute } from 'vue-router'
      import { ElMessage } from 'element-plus'
      import QRCode from 'qrcode'
      import * as echarts from 'echarts'
      import { useUserStore } from '@/store/modules/user'
      import { getDetailssApi, getDetailsssApi } from "@/api/training"
      import { uploadApi } from "@/api/user"
      import html2canvas from 'html2canvas'
      import { dataDecrypt, dataEncrypt } from "@/utils/secret"
      
      // 导入评分图片
      import smasImg from '@/assets/img/entranceAssessment/as.png'
      import smaImg from '@/assets/img/entranceAssessment/a.png'
      import smbsImg from '@/assets/img/entranceAssessment/bs.png'
      import smbImg from '@/assets/img/entranceAssessment/b.png'
      import smcImg from '@/assets/img/entranceAssessment/c.png'

      import asImg from '@/assets/img/entranceAssessment/pyramidas.png'
      import aImg from '@/assets/img/entranceAssessment/pyramida.png'
      import bsImg from '@/assets/img/entranceAssessment/pyramidbs.png'
      import bImg from '@/assets/img/entranceAssessment/pyramidb.png'
      import cImg from '@/assets/img/entranceAssessment/pyramidc.png'
      
      const router = useRouter()
      const route = useRoute()
      const userStore = useUserStore()
      
      // 响应式数据
      const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
      const learnNow = computed(() => userStore.learnNow || {})
      // const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
      const queryData = route.query 
      
      // 模板引用
      const mobileContentRef = ref<HTMLDivElement>()
      const mobileChart1 = ref<HTMLDivElement>()
      const mobileChart2 = ref<HTMLDivElement>()
      const timeChart = ref<HTMLDivElement>()
      const mobileQrCanvas = ref<HTMLCanvasElement>()
      const mobileQrContainer = ref<HTMLDivElement>()
      
      // 状态
      const loading = ref(false)
      const screenshotLoading = ref(false)
      const uploadedImageUrl = ref<string>('')
      
      // 清理函数存储
      const chartCleanupFunctions = ref<(() => void)[]>([])
      
      // 测试报告数据
      const reportData:any = reactive({
        avatar:"",     //头像
        nickName:"",    //名称
        termId:"",
        gradeId:"",
        trainingId : "",
        sourceId : "",
        bookId : "",
        subject : 0,
        source : 0,
        title : "",
        correct : "",
        mistake : "",
        correctRate : "",
        quesCount : 0,
        degree : null,
        score : "0.0",
        status : 0,
        spanTime : "",
        createTime : "",
        times : null,
        trainTime : "0",
        reportJson : {
          pointJson :[
            {pointName: '知识点1'},
            {pointName: '知识点6'},
            {pointName: '知识点333333333333333333333333'},
            {pointName: '知识点3'},
          ],
          quesDegree :[],
          pointVos : null
        },
        items : [],
        reviseCount : 0,
        chapterTrainType : 0,
        content : null,
        pointIds : [],
        integral : null,
        hierarchy : 0
      })
      
      // 格式化后的时间
      const formattedTrainTime = computed(() => formatTime(reportData?.trainTime * 1))
      
      // 是否超时
      const isOvertime = computed(() => {
        const actualMinutes = parseInt(formattedTrainTime.value.value) || 0
        const limitMinutes = reportData.times || 25
        return actualMinutes > limitMinutes
      })
      
      // 获取超时文本
      const getOvertimeText = () => {
        const actualMinutes = parseInt(formattedTrainTime.value.value) || 0
        const limitMinutes = reportData.times || 25
        const overtime = actualMinutes - limitMinutes
        return `${overtime}分钟`
      }
      // 根据分数获取对应的图片
      const getScoreImage = (score: number) => {
        if (score >= 90) return asImg    // A+ 图片
        if (score >= 80) return aImg     // A 图片
        if (score >= 70) return bsImg    // B+ 图片
        if (score >= 60) return bImg     // B 图片
        return cImg                      // C 图片（60分以下）
      }
      // 工具函数
      const getScoreImages = (score: number) => {
        if (score >= 90) return smasImg
        if (score >= 80) return smaImg
        if (score >= 70) return smbsImg
        if (score >= 60) return smbImg
        return smcImg
      }
      
      const formatScore = (score: number): string => {
        if (typeof score !== 'number') return '0'
        const fixed = Number(score.toFixed(1))
        if (fixed % 1 === 0) {
          return fixed.toString()
        }
        return fixed.toFixed(1)
      }
      
      const formatPercentage = (value: number): number => {
        value = value * 1
        if (typeof value !== 'number' || isNaN(value)) return 0
        return Math.min(Math.max(Math.round(value), 0), 100)
      }
      
      // 添加一个专门用于格式化占比的函数
      const formatRatio = (ratio: any, totalRatio: any): string => {
        if (!ratio || ratio === '-') return '-'
        
        try {
          const numRatio = Number(ratio)
          const numTotalRatio = Number(totalRatio)
          
          if (isNaN(numRatio)) return '-'
          
          // 如果总ratio有效，计算百分比
          if (numTotalRatio > 0) {
            return formatScore((numRatio / numTotalRatio) * 100)
          } 
          // 否则直接显示ratio值
          else {
            return formatScore(numRatio)
          }
        } catch (error) {
          console.error('格式化占比出错:', error)
          return '-'
        }
      }
      
      const getKnowledgeColor = (accuracy: number): string => {
        if (accuracy >= 90) return '#00D4AA'
        if (accuracy >= 80) return '#FFA726' 
        if (accuracy >= 70) return '#FF7043'
        return '#F44336'
      }
      
      const formatTime = (milliseconds: number): { value: string, unit: string } => {
        if (!milliseconds || typeof milliseconds !== 'number') return { value: '0', unit: '秒' }
        
        const totalSeconds = milliseconds / 1000
        
        if (totalSeconds < 60) {
          if (totalSeconds % 1 === 0) {
            return { value: totalSeconds.toString(), unit: '秒' }
          }
          return { value: totalSeconds.toFixed(1), unit: '秒' }
        }
        
        if (totalSeconds < 3600) {
          const minutes = totalSeconds / 60
          if (minutes % 1 === 0) {
            return { value: minutes.toString(), unit: '分钟' }
          }
          return { value: minutes.toFixed(1), unit: '分钟' }
        }
        
        const hours = totalSeconds / 3600
        if (hours % 1 === 0) {
          return { value: hours.toString(), unit: '小时' }
        }
        return { value: hours.toFixed(1), unit: '小时' }
      }
      
      // 根据答题时长确定显示的单位
      const getTimeUnit = () => {
        // 获取formattedTrainTime中的单位
        const timeUnit = formattedTrainTime.value?.unit || '秒';
        
        // 根据单位返回对应的显示单位
        if (timeUnit === '秒') {
          return 's'; // 秒
        } else if (timeUnit === '分钟') {
          return 'min'; // 分钟
        } else if (timeUnit === '小时') {
          return 'h'; // 小时
        }
        
        // 默认返回分钟
        return 'min';
      }
      
      const getStatus = (status) => { 
        if (status == 1) { 
          return '已掌握'
        }else  if (status == 2) { 
          return '不过关'
        }else  if (status == 3) { 
          return '未掌握'
        }
      }
      
      const getStatusClass = (status) => { 
        if (status == 1) { 
          return 'mastered'
        }else  if (status == 2) { 
          return 'not-mastered'
        }else  if (status == 3) { 
          return 'average'
        }
      }
      
      // 根据keyPointType值转换为对应的知识点类型文本
      const getKeyPointTypeName = (type) => {
        if (type === 0 || type === '0') {
          return '高频知识点';
        } else if (type === 1 || type === '1') {
          return '重点知识点';
        } else if (type === 2 || type === '2') {
          return '难点知识点';
        }
        return '-';
      }
      
      // 图表相关
      const createMobileChart1 = () => {
        nextTick(() => {
          if (!mobileChart1.value) return
          // 确保数据有效
          const correct = Number(reportData?.correct) || 0
          const mistake = Number(reportData?.mistake) || 0
          const quesCount = Number(reportData?.quesCount) || 1
          
          try {
            const chart = echarts.init(mobileChart1.value)
            const option = {
              animation: true,
              animationDuration: 1000,
              series: [
                {
                  type: 'pie',
                  radius: ['60%', '80%'],
                  center: ['50%', '50%'],
                  startAngle: 90,
                  data: [
                    {
                      value: correct,
                      name: '答对',
                      itemStyle: {
                        color: '#00D4AA',
                        borderRadius: [0, 0, 0, 0]
                      }
                    },
                    {
                      value: mistake,
                      name: '答错',
                      itemStyle: {
                        color: '#E5E5E5',
                        borderRadius: [0, 0, 0, 0]
                      }
                    }
                  ],
                  label: {
                    show: false
                  },
                  labelLine: {
                    show: false
                  },
                  silent: true,
                  emphasis: {
                    disabled: true
                  }
                }
              ],
              graphic: [
                {
                  type: 'text',
                  left: 'center',
                  top: 'center',
                  style: {
                    text: `${correct}/${quesCount}`,
                    textAlign: 'center',
                    fill: '#00D4AA',
                    fontSize: 12,
                    fontWeight: 'bold'
                  }
                }
              ]
            }
            
            chart.setOption(option)
            
            // 立即执行一次resize确保图表正确渲染
            setTimeout(() => {
              chart.resize()
            }, 100)
            
            const resizeChart = () => {
              chart.resize()
            }
            window.addEventListener('resize', resizeChart)
            
            const cleanup = () => {
              window.removeEventListener('resize', resizeChart)
              chart.dispose()
            }
            chartCleanupFunctions.value.push(cleanup)
          } catch (error) {
            console.error('初始化mobileChart1失败:', error)
          }
        })
      }
      
      const createMobileChart2 = () => {
        nextTick(() => {
          if (!mobileChart2.value) return
          
          // 添加调试信息
          console.log('初始化mobileChart2', mobileChart2.value, reportData?.correctRate)
          
          // 确保数据有效
          const correctRate = Number(reportData?.correctRate) || 0
          
          try {
            const chart = echarts.init(mobileChart2.value)
            
            const option = {
              animation: true,
              animationDuration: 1000,
              series: [
                {
                  type: 'pie',
                  radius: ['60%', '80%'],
                  center: ['50%', '50%'],
                  startAngle: 90,
                  data: [
                    {
                      value: correctRate,
                      name: '得分',
                      itemStyle: {
                        color: '#00D4AA',
                        borderRadius: [0, 0, 0, 0]
                      }
                    },
                    {
                      value: Math.max(0, 100 - correctRate), // 确保不为负数
                      name: '未得分',
                      itemStyle: {
                        color: '#E5E5E5',
                        borderRadius: [0, 0, 0, 0]
                      }
                    }
                  ],
                  label: {
                    show: false
                  },
                  labelLine: {
                    show: false
                  },
                  silent: true,
                  emphasis: {
                    disabled: true
                  }
                }
              ],
              graphic: [
                {
                  type: 'text',
                  left: 'center',
                  top: 'center',
                  style: {
                    text: `${correctRate}/100`,
                    textAlign: 'center',
                    fill: '#00D4AA',
                    fontSize: 12,
                    fontWeight: 'bold'
                  }
                }
              ]
            }
            
            chart.setOption(option)
            
            // 立即执行一次resize确保图表正确渲染
            setTimeout(() => {
              chart.resize()
            }, 100)
            
            const resizeChart = () => {
              chart.resize()
            }
            window.addEventListener('resize', resizeChart)
            
            const cleanup = () => {
              window.removeEventListener('resize', resizeChart)
              chart.dispose()
            }
            chartCleanupFunctions.value.push(cleanup)
          } catch (error) {
            console.error('初始化mobileChart2失败:', error)
          }
        })
      }
      
      const createTimeChart = () => {
  nextTick(() => {
    if (!timeChart.value) return   
    try {
      const chart = echarts.init(timeChart.value)
      
      // 获取规定时长（分钟）
      const limitMinutes = reportData.times || 25
      
      // 获取实际答题时长，并根据单位进行转换
      let actualValue = parseFloat(formattedTrainTime.value.value) || 0
      const timeUnit = formattedTrainTime.value.unit
      
      // 将所有时间转换为相同单位进行比较
      let actualMinutes, overtimeMinutes, normalMinutes
      
      if (timeUnit === '秒') {
        // 如果是秒，转换为分钟进行比较
        actualMinutes = actualValue / 60
        overtimeMinutes = Math.max(0, actualMinutes - limitMinutes)
        normalMinutes = Math.min(actualMinutes, limitMinutes)
      } else if (timeUnit === '小时') {
        // 如果是小时，转换为分钟进行比较
        actualMinutes = actualValue * 60
        overtimeMinutes = Math.max(0, actualMinutes - limitMinutes)
        normalMinutes = Math.min(actualMinutes, limitMinutes)
      } else {
        // 默认是分钟
        actualMinutes = actualValue
        overtimeMinutes = Math.max(0, actualMinutes - limitMinutes)
        normalMinutes = Math.min(actualMinutes, limitMinutes)
      }
      
      // 根据实际单位转换显示的数值
      const displayUnit = getTimeUnit()
      let displayLimitValue, displayActualValue
      
      if (displayUnit === 's') {
        // 显示为秒
        displayLimitValue = limitMinutes * 60
        displayActualValue = parseFloat(formattedTrainTime.value.value)
      } else if (displayUnit === 'h') {
        // 显示为小时
        displayLimitValue = limitMinutes / 60
        displayActualValue = parseFloat(formattedTrainTime.value.value)
      } else {
        // 显示为分钟
        displayLimitValue = limitMinutes
        displayActualValue = actualMinutes
      }
    
    const option = {
      grid: {
        left: '20%',
        right: '20%',
        top: '15%',
        bottom: '25%'
      },
      barCategoryGap: '40%',
      xAxis: {
        type: 'category',
        data: ['规定时长', '答题时长'],
        axisLabel: {
          fontSize: 12,
          color: '#666',
          formatter: function(value, index) {
            // 根据索引返回不同的富文本格式
            if (index === 0) {
              return '{a|' + value + '}'; // 规定时长右对齐
            } else {
              return '{b|' + value + '}'; // 答题时长左对齐
            }
          },
          rich: {
            a: {
              align: 'right', // 右对齐
              padding: [0, 10, 0, 0] // 右边增加一些内边距
            },
            b: {
              align: 'left', // 左对齐
              padding: [0, 0, 0, 10] // 左边增加一些内边距
            }
          },
          margin: 5
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        show: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: true,
          fontSize: 10,
          color: '#999',
          formatter: function(value) {
            // 根据实际单位显示不同的格式
            const unit = getTimeUnit();
            if (value === 0) return '0';
            return value + unit;
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            type: 'solid'
          }
        }
      },
      series: [
        // 规定时长柱
        {
          name: '规定时长',
          type: 'bar',
          stack: 'total',
          data: [displayLimitValue, null],
          itemStyle: {
            color: '#7BA8FF',
            marginLeft:'15px'
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              return params.data !== null && params.data !== 0 ? params.data : ''
            },
            fontSize: 12,
            fontWeight: 'bold',
            color: '#333',
            // marginLeft:'15px'
          },
          barWidth: '37px'
        },
        // 答题时长正常部分
        {
          name: '答题时长正常',
          type: 'bar',
          stack: 'answer',
          data: [null, displayActualValue],
          itemStyle: {
            color: '#7BA8FF'
          },
          barWidth: '37px',
          label: {
            show: false
          }
        },
        // 答题时长超时部分
        {
          name: '答题时长超时',
          type: 'bar',
          stack: 'answer',
          data: [null, 0], // 暂时不显示超时部分，因为单位可能不一致
          itemStyle: {
            color: '#FF9999'
          },
          barWidth: '37px',
          label: {
            show: overtimeMinutes > 0,
            position: 'top',
            formatter: function() {
              return displayActualValue
            },
            fontSize: 12,
            fontWeight: 'bold',
            color: '#333'
          }
        }
      ]
    }

    // 如果超时，添加标注
    // if (isOvertime.value) {
    //   (option as any).graphic = [
    //     {
    //       type: 'text',
    //       left: '75%',
    //       top: '25%',
    //       style: {
    //         text: `超时${overtimeMinutes}分`,
    //         fontSize: 12,
    //         fontWeight: 'bold',
    //         fill: '#FF6B6B'
    //       }
    //     },
    //     {
    //       type: 'line',
    //       left: '70%',
    //       top: '30%',
    //       shape: {
    //         x1: 0,
    //         y1: 0,
    //         x2: -15,
    //         y2: 15
    //       },
    //       style: {
    //         stroke: '#FF6B6B',
    //         lineWidth: 1
    //       }
    //     }
    //   ]
    // }
    
    chart.setOption(option)
    
    // 立即执行一次resize确保图表正确渲染
    setTimeout(() => {
      chart.resize();
    }, 100)
    
    const resizeChart = () => {
      chart.resize();
    }
    window.addEventListener('resize', resizeChart)
    
    const cleanup = () => {
      window.removeEventListener('resize', resizeChart)
      chart.dispose()
    }
    chartCleanupFunctions.value.push(cleanup)
    } catch (error) {
      console.error('初始化timeChart失败:', error)
    }
  })
}
      
      // 二维码相关
      const generateMobileQRCode = async () => {
        await nextTick()
        
        if (!mobileQrCanvas.value) return
        
        try {
          let shareUrl = window.location.href
          
          // if (uploadedImageUrl.value) {
          //   shareUrl = uploadedImageUrl.value
          // } else {
          //   shareUrl = await captureAndUpload()
          // }
          
          await QRCode.toCanvas(mobileQrCanvas.value, shareUrl, {
            width: 120,
            height: 120,
            margin: 1,
            color: {
              dark: '#000000',
              light: '#FFFFFF'
            }
          })
          
        } catch (error) {
          console.error('生成二维码失败:', error)
          if (mobileQrContainer.value) {
            mobileQrContainer.value.innerHTML = '<i class="el-icon-qrcode" style="font-size: 40px; color: #999;"></i>'
          }
        }
      }
      
      // 截图上传功能
      const captureAndUpload = async (): Promise<string> => {
        try {
          screenshotLoading.value = true
          
          const mobileContent = mobileContentRef.value
          if (!mobileContent) {
            throw new Error('找不到要截图的元素')
          }
      
          // 隐藏二维码区域
          const qrCard = document.querySelector('.qr-card') as HTMLElement
          const originalQrDisplay = qrCard ? qrCard.style.display : ''
          if (qrCard) {
            qrCard.style.display = 'none'
          }
      
          const canvas = await html2canvas(mobileContent, {
            backgroundColor: '#ffffff',
            scale: 1,
            logging: false,
            useCORS: true,
            allowTaint: true,
            foreignObjectRendering: false,
            imageTimeout: 60000
          })
      
          // 恢复二维码区域显示
          if (qrCard) {
            qrCard.style.display = originalQrDisplay
          }
      
          return new Promise((resolve, reject) => {
            canvas.toBlob(async (blob: Blob | null) => {
              if (!blob) {
                reject(new Error('截图生成失败'))
                return
              }
      
              try {
                const formData = new FormData()
                const fileName = `mobile_test_report_${reportData.trainingId}_${Date.now()}.png`
                formData.append('file', blob, fileName)
      
                const uploadResponse = await uploadApi(formData) as any
                
                if (uploadResponse.code === 200 && uploadResponse.data?.url) {
                  const imageUrl = `${uploadResponse.data.url}${uploadResponse.data.key}`
                  uploadedImageUrl.value = imageUrl
                  resolve(imageUrl)
                } else {
                  reject(new Error('上传失败'))
                }
              } catch (error) {
                reject(error)
              }
            }, 'image/png', 0.8)
          })
        } catch (error) {
          console.error('截图失败:', error)
          ElMessage.error('截图失败: ' + (error as Error).message)
          return `${window.location.origin}/report/share?testId=${reportData.testId || 'demo'}&score=${reportData.score}`
        } finally {
          screenshotLoading.value = false
        }
      }
      
      // 页面操作
      const goBack = () => {
        router.go(-1)
      }
      
      const onGovideo = (val:any) =>{
        router.push({
          path: '/note/wkvideo2',
          query: {
            pointId: val.pointId,
            type:'note',
            subject:reportData.subject,
            noteId:queryData.reportId,
            isShow:'hide'
          }
        })
      }
      
      const answerRecord = () => { 
        router.push({
          path: '/ai_percision/entrance_assessment/answer_record',
          query: {
            data: dataEncrypt({
              reportId: queryData.reportId,
              pageSource: '1'
            }),
          }
        })
      }
      
      const continueStudy = () => {
        router.push({
          path: '/note/note_list'
        })
      }
      
      // 数据获取
      const getData = async () => {
        try {
          loading.value = true
          // queryData.reportId = '1955448945149333506'
          // trainingId=&idNumber=261860
          const res: any = await getDetailsssApi({trainingId:'1956189864400113666',idNumber:'261860'})
          
          if (res.code == 200) {
            // console.log('获取到报告数据:', res.data)
            // 记录知识点数据，帮助调试
            // console.log('知识点数据:', res.data.reportJson?.pointJson)
            // console.log('总ratio:', res.data.ratio)
            Object.assign(reportData, res.data)
            
            // 确保数据已经更新到reactive对象
            await nextTick()
            
            // 延迟初始化图表，确保DOM已经渲染
            setTimeout(() => {
              createMobileChart1()
              createMobileChart2()
              createTimeChart()
              
              setTimeout(() => {
                generateMobileQRCode()
              }, 1000)
            }, 300)
          } else {
            console.error('获取报告数据失败:', res.msg)
          }
        } catch (error) {
          console.error('数据获取失败:', error)
        } finally {
          loading.value = false
        }
      }
      
      // 计算已掌握和需加强的知识点数量
      const masteredPointsCount = computed(() => {
        if (!reportData?.reportJson?.pointJson) return 0;
        return reportData.reportJson.pointJson.filter(point => point.status === 1).length;
      });

      const needImprovePointsCount = computed(() => {
        if (!reportData?.reportJson?.pointJson) return 0;
        return reportData.reportJson.pointJson.filter(point => point.status !== 1).length;
      });

      const totalPointsCount = computed(() => {
        if (!reportData?.reportJson?.pointJson) return 0;
        return reportData.reportJson.pointJson.length;
      });
      
      // 生命周期
      onMounted(() => {
        // console.log('组件挂载，开始获取数据，reportId:', queryData.reportId,queryData)
        // console.log('图表容器元素:', mobileChart1.value, mobileChart2.value, timeChart.value)
        if (queryData?.token) {
          localStorage.token = queryData.token
          userStore.token = queryData.token
        }
        // 确保DOM已经渲染完成
        nextTick(() => {
          console.log('DOM已更新，图表容器元素:', mobileChart1.value, mobileChart2.value, timeChart.value)
        })
        
        getData()
        reportData.testId = route.query.testId as string || 'demo'
        reportData.bookId = route.query.bookId as string || 'demo'
      })
      
      onUnmounted(() => {
        chartCleanupFunctions.value.forEach(cleanup => cleanup())
      })
      </script>
      
      <style lang="scss" scoped>
      .mobile-report-main {
        width: 100vw;
        min-height: 100vh;
        background: linear-gradient(180deg, #6B9AFF 0%, #7BA8FF 50%, #9BBFFF 100%);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
      }
      
      .mobile-header {
        display: flex;
        // align-items: center;
        justify-content: space-between;
        padding: 24px 16px 16px 16px;
        background: transparent;
        position: relative;
        // z-index: 10;
        height: 230px;
        background: url(@/assets/img/synchronous/topNav.png) no-repeat;
        background-size: 100% 100%;
        .header-back {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
        
        .header-title {
          font-size: 16px;
          font-weight: 600;
          color: white;
          text-align: center;
          padding-top: 6px;
          position: absolute;
          top: 30px;
          left: 50%;
          transform: translateX(-50%);
        }
        .test-date-tag{
          color: #fff;
          font-size: 12px;
          padding: 6px 12px;
          background: #00000033;
          border-radius: 16px;
          align-self: flex-start;
          margin-top: 160px;
          margin-right: auto;
          position: absolute;
          left: 30px;
          bottom: 60px;
        }
        .header-placeholder {
          width: 40px;
        }
      }
      
      .mobile-content {
        padding: 0 16px 20px 16px;
        margin-top: -30px;
      }
      
      .user-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 16px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        position: relative;
        
        .user-info {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          
          .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 25px;
            overflow: hidden;
            margin-right: 12px;
            position: absolute;
            top: -20px ;
            left: 20px;
            .avatar-img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          
          .user-details {
            flex: 1;
            padding-top: 30px;
            .user-name {
              font-size: 16px;
              font-weight: 600;
              color: rgba(50, 58, 87, 1);
              margin-bottom: 4px;
              .semester{
                font-size: 12px;
                color: #5A85EC;
                background: #E5F2FD;
                padding: 4px 8px;
                border-radius: 12px;
                font-weight: 500;
                margin-left: 10px;
              }
            }
            
            .user-grade {
              font-size: 14px;
              color: #5A85EC;
              background: #E5F2FD;
              padding: 2px 8px;
              border-radius: 10px;
              display: inline-block;
              margin-bottom: 4px;
            }
            
            .test-date {
              font-size: 12px;
              color: #666;
            }
          }
        }
         .score-fs{
           position: absolute;
           top: 20px;
           right: 20px;
           display: flex;
           align-items: baseline;
           line-height: 1;
           
           .score-number{
             font-size: 40px;
             color: rgba(255, 32, 85, 1);
             line-height: 1;
             font-weight: 700;align-self: flex-end;
           }
           
           .score-unit{
             color: rgba(255, 32, 85, 1);
             font-size: 14px;
             line-height: 1;
             margin-left: 2px;
             align-self: flex-end;
            padding-bottom: 5px;
           }
         }
        .score-display {
          display: flex;
          justify-content: center;
          margin-bottom: 20px;
          
          .score-circle {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            
            &::before {
              content: '';
              position: absolute;
              inset: 8px;
              background: white;
              border-radius: 52px;
            }
            
            .score-number {
              font-size: 36px;
              font-weight: 700;
              color: #5A85EC;
              position: relative;
              z-index: 2;
              line-height: 1;
            }
            
            .score-unit {
              font-size: 14px;
              color: #5A85EC;
              position: relative;
              z-index: 2;
              margin-top: 4px;
            }
          }
        }
        
        .test-content {
          margin-bottom: 20px;
          text-align: left;
          border-bottom: 2px solid #f1f1f1;
          padding-bottom: 20px;
          .content-label {
            color: rgba(112, 121, 126, 1);
            font-size: 12px;
            font-weight: 500;
          }
          
          .content-text {
            color: rgba(112, 121, 126, 1);
            font-size: 12px;
            margin-left: 4px;
          }
        }
        
        .grade-display {
          text-align: left;
          display: flex;
          
          .grade-text {
            font-size: 14px;
            color: rgba(50, 58, 87, 1);
            margin-bottom: 12px;
            font-weight: 500;
            padding-top: 10px;
          }
          
          .grade-image {
            width: 120px;
            height: auto;
          }
        }
      }
      
      .stats-card, .knowledge-card, .weakness-card, .suggestion-card {
        background: #fff;
        border-radius: 16px;
        // padding: 20px;
        margin-bottom: 16px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        padding-top: 10px;
      }
      
      .stats-title, .card-title {
        margin-bottom: 20px;
        // background: red;
        background: #cf4393;
        line-height: 25px;
        border-radius: 0 20px 20px 0;
        width: 120px;
        color: #fff;
        .title-number {
          font-size: 12px;
          font-weight: 700;
          color: #fff;
          margin-right: 8px;
          padding-left: 5px;
        }
        
        .title-text {
          font-size: 12px;
          font-weight: 600;
         color: #fff;
        }
      }
      
      .stats-grid {
      //   display: grid;
      //   grid-template-columns: 1fr 1fr;
      //   gap: 16px;
        
        // margin-bottom: 20px;
        padding:0 20px;
        .stat-item {
          display: flex;
          align-items: center;
          background: rgba(243, 246, 250, 1);
          padding: 0px 30px;
          border-radius: 12px;
          margin-bottom: 10px;
          
          .stat-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            
            .stat-label {
              font-size: 14px;
              color: rgba(50, 58, 87, 1);
              margin-left: 4px;
            }
          }
          
          .stat-chart {
            margin-bottom: 8px;
            
            // 修复1: 确保图表容器有明确的宽高和背景色
            .chart-container {
              width: 150px;
              height: 150px;
              margin: 0 auto;
              // background-color: #fff; // 添加背景色
              border-radius: 8px; // 添加圆角
            }
          }
          
          .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #5A85EC;
          }
        }
      }
      
      .detailed-stats {
        padding:0 20px;
        .stats-row {
          display: flex;
          // justify-content: space-around;
          margin-bottom: 16px;
          background: rgb(243,246,250);;
          border-radius: 5px;
          padding: 0px 10px 0px 30px;
          
          .stats-item {
            display: flex;
            align-items: center;
            
            .stats-label {
              font-size: 14px;
              color: rgba(50, 58, 87, 1);
              margin: 0 8px;
            }
            
            .stats-number {
              font-size: 14px;
              font-weight: 500;
              color: rgba(50, 58, 87, 1);
              margin-left: 8px;
            }
          }
        }
        
        .stats-circles {
        //   display: flex;
        //   justify-content: space-around;
          margin-bottom: 20px;
          margin-left: auto;
          margin-right: 20px;
          margin-top: 20px;
          .circle-stat {
            display: flex;
            align-items: center;
            
            .circle-label {
              display: flex;
              align-items: center;
              border-radius: 10px;
              font-size: 12px;
              color: #666;
              margin-bottom: 8px;
              background: #fff;
              padding: 5px;
              width: 110px;
              text-align: center;
              .dot{
                width: 6px;
                height: 6px;
                border-radius: 100%;
                margin-right: 5px;
                margin-left: 10px;
                background: red;
              }
            }
            
            .circle-value {
              width: 36px;
              height: 36px;
              border-radius: 18px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              font-weight: 600;
              color: white;
              margin: 0 auto;
              
              &.total {
                background: #9E9E9E;
              }
              
              &.correct {
                background: #00D4AA;
              }
              
              &.wrong {
                background: #FF6B6B;
              }
            }
          }
        }
        
        .time-stats {
          .time-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .time-flex {
              display: flex;
              align-items: center;
              
              .time-label {
                font-size: 14px;
                color: #333;
                margin-left: 4px;
              }
            }
            
            .time-value {
              font-size: 16px;
              font-weight: 600;
              color: #ff6b6b;
              
              .time-unit {
                font-size: 12px;
                margin-left: 2px;
              }
            }
          }
        }
        
        .time-chart-section {
          background: rgb(243,246,250);
          border-radius: 8px;
          padding:20px 30px;
          margin-top: 16px;
          
          .time-chart-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            
            .time-chart-title {
              font-size: 14px;
              color: #333;
              margin-left: 8px;
              font-weight: 500;
            }
          }
          
          .time-chart-content {
            position: relative;
            
            .time-chart-container {
              width: 100%;
              height: 140px;
              margin-bottom: 8px;
              background-color: transparent;
              border-radius: 8px;
              position: relative;
            }
            
            .chart-unit {
              text-align: left;
              font-size: 10px;
              padding-bottom: 6px;
              color: rgba(112, 121, 126, 1);
            }
          }
          
          .overtime-warning {
            margin-top: 12px;
            text-align: center;
            font-size: 12px;
            color: rgba(255, 32, 85, 1);
            font-weight: 500;
            margin-top: 30px;
          }
        }
      }
      
      .knowledge-list {
        padding: 0 20px 20px 20px;
        margin-bottom: 20px;
        display: flex;
        flex-wrap: wrap;
        .knowledge-item {
          margin-right: 10px;
          margin-top: 10px;               
          &:last-child {
            margin-bottom: 0;
          }
          
          .knowledge-name {
            border-radius: 22px;
            padding: 6px 10px;
            display: block;
            font-size: 14px;
            color: #fff;
            
            &.mastered {
              background: rgba(35, 203, 137, 1); // 绿色背景 - 已掌握
            }
            
            &.need-improve {
              background: rgba(243, 174, 62, 1); // 黄色背景 - 需加强
            }
          }
          
          // .knowledge-progress {
          //   display: flex;
          //   align-items: center;
            
          //   .progress-bar {
          //     flex: 1;
          //     height: 8px;
          //     background: #f0f0f0;
          //     border-radius: 4px;
          //     overflow: hidden;
          //     margin-right: 8px;
              
          //     .progress-fill {
          //       height: 100%;
          //       border-radius: 4px;
          //       transition: width 0.3s ease;
          //     }
          //   }
            
          //   .progress-text {
          //     font-size: 12px;
          //     color: #666;
          //     font-weight: 500;
          //     min-width: 40px;
          //   }
          // }
        }
      }
              .weakness-table {
  padding: 12px;
  
  .table-header {
    display: grid;
    grid-template-columns: 70px 1fr 80px 40px 40px;
    border: 1px solid rgba(215, 217, 222, 1);
    
    .header-cell {
      padding: 10px 0;
      font-size: 12px;
      font-weight: 600;
      color: rgba(112, 121, 126, 1);
      text-align: center;
      border-right: 1px solid rgba(215, 217, 222, 1);
      background-color: rgba(248, 249, 250, 1);
      
      &:last-child {
        border-right: none;
      }
    }
  }
  
  .table-row {
    display: grid;
    grid-template-columns: 70px 1fr 80px 40px 40px;
    border-left: 1px solid rgba(215, 217, 222, 1);
    border-right: 1px solid rgba(215, 217, 222, 1);
    border-bottom: 1px solid rgba(215, 217, 222, 1);
    
    &:nth-child(even) {
      background-color: rgba(250, 250, 250, 0.5);
    }
    
    &:hover {
      background-color: rgba(229, 242, 253, 0.3);
    }
    
    &:last-child {
      border-bottom: 1px solid rgba(215, 217, 222, 1);
    }
    
    .table-cell {
      font-size: 12px;
      color: rgba(50, 58, 87, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 0;
      border-right: 1px solid rgba(215, 217, 222, 1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      
      &:last-child {
        border-right: none;
      }
      
      &.point-name {
        justify-content: flex-start;
        line-height: 1.3;
        padding-left: 10px;
        padding-right: 5px;
        white-space: normal;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        
      }
    }
  }
}
      
      .status-tag {
        padding: 4px 8px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: 500;
        color: white;
        text-align: center;
        
        &.not-mastered {
          background: linear-gradient(to right, #F07F4C, #C95656);
        }
        
        &.average {
          background: linear-gradient(to right, #F6D22B, #F29500);
        }
        
        &.mastered {
          background: linear-gradient(to right, #08D8B8, #00B392);
        }
      }
      
      .key-point-type {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        color: rgba(50, 58, 87, 1);
        text-align: center;
        white-space: nowrap;
        
        // &.type-0 {
        //   background-color: #00C9A3; // 高频知识点 - 绿色
        // }
        
        // &.type-1 {
        //   background-color: #007FE9; // 重点知识点 - 蓝色
        // }
        
        // &.type-2 {
        //   background-color: #FF9500; // 难点知识点 - 橙色
        // }
      }
      
      .suggestion-content {
        .suggestion-header {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12px;
        }
        
        .suggestion-desc {
          font-size: 14px;
          color: #333;
          text-align: center;
          margin-bottom: 16px;
          line-height: 1.4;
        }
        
        .suggestion-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 20px;
          padding: 20px 14px;

          
          .suggestion-tag {
            background: rgba(229, 242, 253, 1);
            color: rgba(0, 127, 233, 1);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s ease;
            
            &:active {
              background: #E0E0E0;
            }
          }
        }
        
        .suggestion-buttons {
          display: flex;
          gap: 12px;
          
          button {
            flex: 1;
            padding: 12px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: opacity 0.3s ease;
            
            &:active {
              opacity: 0.7;
            }
            
            &.btn-record {
              background: #F5F5F5;
              color: #666;
            }
            
            &.btn-correct {
              background: #00C9A3;
              color: white;
            }
          }
        }
      }
      
      .qr-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        padding: 30px 20px 50px 20px ;
        text-align: center;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        
        .qr-title {
          font-size: 18px;
          font-weight: 600;
          color: rgba(50, 58, 87, 1);
          margin-bottom: 43px;
        }
        
        .qr-container {
          display: flex;
          justify-content: center;
          
          canvas {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
          
          .qr-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 120px;
            height: 120px;
            
            .loading-spinner {
              width: 20px;
              height: 20px;
              border: 2px solid #f3f3f3;
              border-top: 2px solid #5A85EC;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin-bottom: 8px;
            }
            
            .loading-text {
              font-size: 12px;
              color: #666;
            }
          }
        }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      // 响应式适配更小的屏幕
      @media (max-width: 375px) {
        .mobile-content {
          padding: 0 12px 20px 12px;
        }
        
        .user-card, .stats-card, .knowledge-card, .weakness-card, .suggestion-card, .qr-card {
          padding: 16px;
        }
        
        .score-circle {
          width: 100px !important;
          height: 100px !important;
          border-radius: 50px !important;
          
          &::before {
            inset: 6px !important;
            border-radius: 44px !important;
          }
          
          .score-number {
            font-size: 30px !important;
          }
        }
      }
      </style> 