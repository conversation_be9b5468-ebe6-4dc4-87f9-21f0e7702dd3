<!-- 名师课堂-章节列表 -->
<template>
  <div class="content">
    <div class="inner">
      <div class="wrap">
        <div class="menu_lt">
          <div class="subbox" v-if="state.subList.length && !query.source">
            <!-- {{ state.subActive }} -->
            <el-select v-model="curSubActive" placeholder="请选择" class="subkey" @change="setSubject">
              <el-option v-for="item in state.subList" :key="item.key" :label="item.text" :value="item.key">
              </el-option>
            </el-select>
          </div>
          <!-- 选中第一节变色，2或3末级变绿，中间不变 -->
          <div class="menu_ul" id="menu_ul" ref="quesBoxRef" @scroll="handleScroll">
            <div class="menu_li" v-for="(item,index) in state.chapterList" :key="index">
              <!-- 第1节 -->
              <div class="menu1" :class="item.active" @click="tog3Menu1" :data-i="index">
                <div class="nowrap">{{item.name}}</div>
                <img src="@/assets/img/teachroom/down.svg" class="menu_img" :class="item.up"
                  v-if="item.children.length" />
              </div>
              <!-- 第2节 -->
              <div class="child" v-show="item.up" v-for="(item2,index2) in item.children" :key="index2">
                <div class="menu2" :class="item2.active" @click="tog3Menu2" :data-i="index" :data-i2="index2"
                  :style="item2.children.length?'background:none;':''">
                     <!-- 督学任务标签 -->
                  <div class="task-badge" v-if="item2?.task"></div>
                  <div class="nowrap" :style="item2.children.length?'color:#333;':''">{{item2.name}}</div>
                  <img src="@/assets/img/teachroom/down.svg" class="menu_img" :class="item2.up"
                    v-if="item2.children.length" />
                </div>
                <!-- 第3节 -->
                <div class="menu3" v-show="item2.up" v-for="(item3,index3) in item2.children" :key="index3">
                  <div class="nowrap" :class="item3.active" @click="tog3Menu3" :data-i="index" :data-i2="index2"
                    :data-i3="index3">{{item3.name}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="menu_rt" id="menu_rt" ref="quesBoxRef2" @scroll="handleScroll2">
          <div class="book">
            <div class="book_ver" v-if=" !query?.source ">
              <span>当前教材：</span>{{state.bookName}}
            </div>
            <div class="book_ver" v-else>
              <span>当前教材：</span>{{ query.editionName }} {{query.gradeName}}{{ query.termName }}
            </div>

            <div class="book_fav" @click="goCollect">
              <img src="@/assets/img/teachroom/star.svg" />我的收藏
            </div>
          </div>
          <div class="wk_wrap" v-if="state.isShow&&state.data.length">
            <div class="wk_box" v-for="(item,index) in state.data">
              <div class="wk_h1">
                <span></span><span v-html="ReplaceMathString(item.name)"></span>
              </div>
              <div class="wk_ul" v-for="(item2,index2) in item.knowledgeList" :key="index2">
                <!-- 带概述 -->  
                <div class="wk_li wk_li2" v-for="(item3,index3) in item2.videoInfos" v-if="item2.videoInfos">
                  <div class="wk_pic" @click="wekePlay" :data-i="index" :data-i2="index2" :data-i3="index3">
                    <img src="@/assets/img/synchronous/task-badge.png" v-if="item3?.task"  class="task-badge" />
                    <img :src="item3.cover" class="wk_img" v-if="item3.cover"  />
                    <img src="@/assets/img/teachroom/novid2.png" class="wk_img" v-else />
                    <img src="@/assets/img/teachroom/play.svg" class="wk_play" />
                  </div>
                  <div class="wk_bom">
                    <div class="wk_tit nowrap" @click="wekePlay" :data-i="index" :data-i2="index2" :data-i3="index3"
                      v-html="ReplaceMathString(item3.videoName)">
                    </div>
                    <div class="wk_state" @click="wekePlay" :data-i="index" :data-i2="index2" :data-i3="index3">
                      <div class="wk_status status2" v-if="item3.studyStatus==2">已学完</div>
                      <div class="wk_status status1" v-else-if="item3.studyStatus==1">未学完</div>
                      <div class="wk_status status0" v-else>未学习</div>
                      <div class="wk_thumbs">
                        <img src="@/assets/img/teachroom/thumbs.svg" />
                        <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                        {{item3.likeNum||0}}
                      </div>
                      <!-- <div class="wk_collect active">
                          <img src="@/assets/img/teachroom/collect.svg" />
                          <img src="@/assets/img/teachroom/collectsel.svg" />
                        </div> -->
                    </div>
                    <div class="wk_point" @click="gaisuShow" :data-i="index" :data-i2="index2" v-if="item2.summary">
                      知识点概述</div>
                  </div>
                </div>
                <!-- 无视频带概述 -->
                <div class="wk_li wk_li2" v-if="!item2.videoInfos">
                  <div class="wk_pic">
                    <img src="@/assets/img/synchronous/task-badge.png" v-if="item2?.task" class="task-badge" />
                    <img src="@/assets/img/teachroom/novid2.png" class="wk_img" />
                  </div>
                  <div class="wk_bom">
                    <div class="wk_tit nowrap">{{item2.knowledgeName}}</div>
                    <div class="wk_point" @click="gaisuShow" :data-i="index" :data-i2="index2" v-if="item2.summary">
                      知识点概述</div>
                  </div>
                </div>
                <!-- 例题 -->
                <div class="wk_li" v-for="(item3,index3) in item2.exampleVideoInfos" :key="index3" @click="wekePlay2"
                  :data-i="index" :data-i2="index2" :data-i3="index3">
                  <div class="wk_pic">
                    <img src="@/assets/img/synchronous/task-badge.png" v-if="item3?.task" class="task-badge" />
                    <img :src="item3.cover" class="wk_img" v-if="item3.cover"  />
                    <img src="@/assets/img/teachroom/novid2.png" class="wk_img" v-else />
                    <img src="@/assets/img/teachroom/play.svg" class="wk_play" />
                  </div>
                  <div class="wk_bom">
                    <div class="wk_tit nowrap" v-html="ReplaceMathString(item3.videoName)"></div>
                    <div class="wk_state">
                      <div class="wk_status status2" v-if="item3.studyStatus==2">已学完</div>
                      <div class="wk_status status1" v-else-if="item3.studyStatus==1">未学完</div>
                      <div class="wk_status status0" v-else>未学习</div>
                      <div class="wk_thumbs">
                        <img src="@/assets/img/teachroom/thumbs.svg" />
                        <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                        {{item3.likeNum||0}}
                      </div>
                      <!-- <div class="wk_collect active">
                          <img src="@/assets/img/teachroom/collect.svg" />
                          <img src="@/assets/img/teachroom/collectsel.svg" />
                        </div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 无数据 -->
          <div class="nodata" v-if="state.isShow&&!state.data.length">
            <img src="@/assets/img/user/nodata.png" />该知识点暂无名师课程！
          </div>
        </div>
      </div>
    </div>
  </div>
  <buyVip :show="state.showVip" @close="state.showVip = false"></buyVip>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, watch,computed } from 'vue';
  import router from '@/router/index'
  import { useUserStore } from "@/store/modules/user"
  import { ElMessage } from "element-plus"
  import { subjectEnum, subjectEnum2, subjectList } from '@/utils/user/enum'
  import { deepClone, ReplaceMathString } from '@/utils/user/util'
  import { getBookChapterApi, getVideoListApi, getVideoReviewNumApi, setUserVideoViewNumberApi } from "@/api/video"
  import { useRoute } from "vue-router"
  import buyVip from "@/views/components/buyVip/index.vue"
  import { setLearnKey } from '@/utils/user/learntime'
  import { useRouteStoreHook } from '@/store/modules/route'

  defineOptions({
    name: "TeachRoomTeachList"
  })

  const route = useRoute()
  const query = reactive<any>(route.query)
  const state : any = reactive({
    showVip: false,
    bookName: '',
    termNameNow: '',
    isShowContent: true,
    loading: true,
    subList: [],
    subActive: '',
    subName: '语文',
    learnNow: {},
    subObject: {},
    sessionObject: {},
    childLen: 0, //章节层级数
    chapterList: [], //课程列表
    chapterArr: [], //课程列表-所有学科
    sortArr: ['语文', '数学', '英语', '物理', '化学', '生物','科学'], // '历史', '道法', '地理', '科学' 初中没有科学
    sortArr2: ['语文', '数学', '英语'], //, '科学'
    isShow: 0,
    isFirst: 1,
    chapterId: '',
    chapterName: '',
    data: [],
    isRefresh: 0,
    scrollTop: 0,
    scrollTop2: 0
  })

const curSubActive = computed({
  get() {
    // 1. 精确匹配
    const exactMatch = state.subList.find((item: any) => item.key === state.subActive);
    if (exactMatch) return exactMatch.key;
    
    // 2. 前缀匹配：如果subActive是某个选项的前缀，或者某个选项是subActive的前缀
    const prefixMatch = state.subList.find((item: any) => 
      item.key.startsWith(state.subActive) || 
      state.subActive.startsWith(item.key)
    );
    if (prefixMatch) return prefixMatch.key;
    
    // 3. 包含匹配：如果某个选项包含subActive字符串
    const includesMatch = state.subList.find((item: any) => 
      item.key.includes(state.subActive)
    );
    if (includesMatch) return includesMatch.key;
    
    // 4. 如果都没找到匹配，返回原始值
    return state.subActive;
  },
  set(value) {
    // 更新状态
    state.subActive = value;
  }
});

  const videoIds: any = computed(() => { 
    const videoIds = route.query.videoIds || '';
    if (!videoIds || typeof videoIds !== 'string') return false;
    
    // 将逗号分隔的章节ID转为数组并检查是否包含当前章节ID
    const chapterIdsArray = videoIds.split(',');
    return chapterIdsArray
  })

  onMounted(() => {
    console.log(query,"query?.sourcequery?.source")
    init()
  })

  //监听路由参数
  watch(
    () => route.query,
    (newQ) => {
      // getVideoList()
      setTop(state.scrollTop2)
      setTop2(state.scrollTop)
      let subName = newQ.subName
      if ((subName != state.subName || state.isRefresh) && route.name == "TeachRoomTeachList") {
        //匹配学科
        for (let i of state.subList) {
          if (i.text == subName) {
            setData({
              subActive: i.key,
              subName,
              isRefresh: 0
            })
            break
          }
        }
        getBookVersion()
        //列表置顶
        setTop(0)
      }
    }
  )

  //记录滚动位置
  const quesBoxRef = ref(null);
  const handleScroll = () => {
    const element : any = quesBoxRef.value;
    if (element) {
      setData({
        scrollTop: element.scrollTop
      })
    }
  }
  const quesBoxRef2 = ref(null);
  const handleScroll2 = () => {
    const element : any = quesBoxRef2.value;
    if (element) {
      setData({
        scrollTop2: element.scrollTop
      })
    }
  }

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }
  //返回置顶
  const setTop = (num:any) => {
    const div : any = document.getElementById('menu_rt');
    if (div) {
      div.scrollTop = num;
    }
  }

  //返回置顶
  const setTop2 = (num : any) => {
    const div : any = document.getElementById('menu_ul');
    if (div) {
      div.scrollTop = num;
    }
  }

  const init = () => {
    //重置数据
    setData({
      subList: [],
      subName: query.subName || '语文',
      subActive: 'chinese3',
      learnNow: {},
      subObject: {},
      sessionObject: {},
      chapterList: []
    })

    const learnNow = JSON.parse(localStorage.learnNow || '{}')
    //无用户返回首页
    if (!learnNow) {
      return
    }
    const gradeId = learnNow?.gradeId || ''
    //匹配科目传参
    setData({
      learnNow,
      gradeId,
      termNameNow: learnNow.termId,
      subObject: learnNow.versions
    })
    if (gradeId > 9) {
      //高中跳学大知识点
      router.replace({ name: "PointRoomIndex" })
    } else {
      setSubList()
    }
  }

  // 判断是否是任务章节
  const isTaskChapter = (chapterId: string) => {
    const yxpChapterIds = route.query.yxpChapterIds || '';
    if (!yxpChapterIds || typeof yxpChapterIds !== 'string') return false;
    
    // 将逗号分隔的章节ID转为数组并检查是否包含当前章节ID
    const chapterIdsArray = yxpChapterIds.split(',');
    return chapterIdsArray.includes(chapterId);
  }

  // 判断会员状态
  const getIsMember = () => {
    const isVip = JSON.parse(localStorage.memberInfo || '{}')
    if (isVip?.isMember) {
      return true
    } else {
      state.showVip = true
      return false
    }
  }

  //获取课程章节
  const getCourseList = () => {
    const { subObject, subActive, chapterArr } = state
    const { bookId } = subObject[subActive]
    // if (chapterArr[bookId]) {
    //   //有缓存
    //   setData({
    //     chapterList: chapterArr[bookId],
    //     chapterArr,
    //     isShow: 1
    //   })
    //   const child : any = [0, 0, 0]
    //   const list = chapterArr[bookId]
    //   if (list.length) {
    //     // 默认展开
    //     for (const i of list) {
    //       child[0] = 1
    //       for (const n of i.children) {
    //         child[1] = 1
    //         for (const m of n.children) {
    //           child[2] = 1
    //         }
    //       }
    //     }
    //     setData({
    //       childLen: child[0] + child[1] + child[2]
    //     })
    //   }
    // } else {
    //没缓存
    setData({
      isShow: 0,
      chapterList: []
    })
    const param = {
      //  bookId,
      bookId:query.bookId?query.bookId:bookId,
      hierarchy: 3,
      chapterIds:query?.chapterId?query?.chapterId:''
    }
    getBookChapterApi(param)
      .then((res : any) => {
        const list = res.data
        const child : any = [0, 0, 0]
        if (list.length) {
          // 默认展开
          for (const i of list) {
            i.active = ''
            i.up = 'up'
            child[0] = 1
            for (const n of i.children) {
              n.active = ''
              n.up = 'up'
              child[1] = 1
              for (const m of n.children) {
                m.active = ''
                m.up = 'up'
                child[2] = 1
              }
            }
          }
          
          // 检查是否有指定的章节ID需要选中
          let id = '', name = ''
          let targetChapterId = query?.chapterId || ''
          let foundTargetChapter = false
          
          if (targetChapterId) {
            // 寻找匹配的章节ID
            outerLoop:
            for (let i = 0; i < list.length; i++) {
              // 检查一级章节
              if (list[i].id === targetChapterId) {
                list[i].active = 'active'
                id = list[i].id
                name = list[i].name
                foundTargetChapter = true
                break outerLoop
              }
              
              // 检查二级章节
              if (list[i].children && list[i].children.length) {
                for (let j = 0; j < list[i].children.length; j++) {
                  if (list[i].children[j].id === targetChapterId) {
                    list[i].active = 'active'
                    list[i].children[j].active = 'active'
                    id = list[i].children[j].id
                    name = list[i].children[j].name
                    foundTargetChapter = true
                    break outerLoop
                  }
                  
                  // 检查三级章节
                  if (list[i].children[j].children && list[i].children[j].children.length) {
                    for (let k = 0; k < list[i].children[j].children.length; k++) {
                      if (list[i].children[j].children[k].id === targetChapterId) {
                        list[i].active = 'active'
                        list[i].children[j].active = 'active'
                        list[i].children[j].children[k].active = 'active'
                        id = list[i].children[j].children[k].id
                        name = list[i].children[j].children[k].name
                        foundTargetChapter = true
                        break outerLoop
                      }
                    }
                  }
                }
              }
            }
          }
          
          // 如果没有找到指定章节或没有指定章节ID，则默认选中第一个
          if (!foundTargetChapter) {
            //默认第一个选中
            list[0].active = 'active'
            id = list[0].id
            name = list[0].name
            if (list[0].children.length) {
              let arr2 = list[0].children[0]
              id = arr2.id
              name = arr2.name
              list[0].children[0].active = 'active'
              if (list[0].children[0].children.length) {
                let arr3 = list[0].children[0].children[0]
                id = arr3.id
                name = arr3.name
                list[0].children[0].children[0].active = 'active'
              }
            }
          }
          
          setData({
            scrollTop: 0,
            childLen: child[0] + child[1] + child[2],
            chapterId: id,
            chapterName: name,
          })
          getVideoList()
        }
        //缓存所有学科章节
        chapterArr[bookId] = list
        setData({
          chapterList: list,
          chapterArr,
          isShow: 0
        })
        
        // 如果找到了指定章节，滚动到对应位置
        if (query?.chapterId) {
          setTimeout(() => {
            scrollToActiveChapter()
          }, 300)
        }
      })
      .catch(() => {
        setData({
          isShow: true,
          chapterList: [],
          data: []
        })
      })
    // }
  }
  
  // 滚动到选中的章节节点
  const scrollToActiveChapter = () => {
    // 获取左侧菜单容器
    const menuContainer = document.getElementById('menu_ul')
    if (!menuContainer) return
    
    // 尝试查找不同级别的活跃节点
    let activeNode: Element | null = null
    
    // 优先尝试查找二级或三级活跃节点（更具体的选择）
    const activeLower = menuContainer.querySelector('.menu2.active, .menu3 .active')
    if (activeLower) {
      activeNode = activeLower
    } else {
      // 如果没有找到二级/三级节点，则查找一级节点
      const activeFirst = menuContainer.querySelector('.menu1.active')
      if (activeFirst) {
        activeNode = activeFirst
      }
    }
    
    // 如果找不到任何活跃节点，直接返回
    if (!activeNode) return
    
    // 计算节点位置
    const containerRect = menuContainer.getBoundingClientRect()
    const nodeRect = activeNode.getBoundingClientRect()
    
    // 检查节点是否在可视区域内
    const isVisible = (
      nodeRect.top >= containerRect.top &&
      nodeRect.bottom <= containerRect.bottom
    )
    
    // 如果节点不在可视区域内，则滚动到该节点
    if (!isVisible) {
      // 计算滚动位置，使节点位于容器中间偏上位置
      const scrollTop = nodeRect.top + menuContainer.scrollTop - containerRect.top - (containerRect.height / 3)
      
      // 平滑滚动到目标位置
      menuContainer.scrollTo({
        top: scrollTop,
        behavior: 'smooth'
      })
    }
  }

  // 1级菜单切换-数学
  const tog3Menu1 = (e : any) => {
    const { i } = e.currentTarget.dataset
    const { chapterList } = state
    const child = chapterList[i]
    if (child?.children.length) {
      //有子级
      chapterList[i].up = child.up ? '' : 'up'
      setData({
        chapterList
      })
    } else {
      //无子级
      const id = child.id, name = child.name
      //选中变色
      let list = chapterList
      for (const x of list) {
        x.active = ''
        for (const y of x.children) {
          y.active = ''
          for (const z of y.children) {
            z.active = ''
          }
        }
      }
      list[i].active = 'active'
      setData({
        chapterList: list,
        chapterId: id,
        chapterName: name
      })
      getVideoList()
      setTop(0)
    }
  }
  // 2级菜单切换-数学
  const tog3Menu2 = (e : any) => {
    const { i, i2 } = e.currentTarget.dataset
    const { chapterList } = state
    const child = chapterList[i]?.children
    if (child[i2]?.children?.length) {
      //有子级
      chapterList[i].children[i2].active = child[i2].active ? '' : 'active'
      chapterList[i].children[i2].up = child[i2].up ? '' : 'up'
      setData({
        chapterList
      })
    } else {
      //无子级
      const id = child[i2].id, name = child[i2].name
      //选中变色
      let list = chapterList
      for (const x of list) {
        x.active = ''
        for (const y of x.children) {
          y.active = ''
          for (const z of y.children) {
            z.active = ''
          }
        }
      }
      list[i].active = 'active'
      list[i].children[i2].active = 'active'
      setData({
        chapterList: list,
        chapterId: id,
        chapterName: name
      })
      getVideoList()
      setTop(0)
    }
  }

  // 3级菜单切换-数学
  const tog3Menu3 = (e : any) => {
    const { i, i2, i3 } = e.currentTarget.dataset
    const { chapterList } = state
    const arr = chapterList[i].children[i2].children[i3]
    const id = arr.id, name = arr.name
    let list = chapterList
    for (const x of list) {
      x.active = ''
      for (const y of x.children) {
        y.active = ''
        for (const z of y.children) {
          z.active = ''
        }
      }
    }
    list[i].active = 'active'
    list[i].children[i2].active = 'active'
    list[i].children[i2].children[i3].active = 'active'
    setData({
      chapterList: list,
      chapterId: id,
      chapterName: name
    })
    getVideoList()
    setTop(0)
  }

  const resourceIds: any = computed(() => { 
    const resourceIds = route.query.videoIds || '';
    console.log(resourceIds,"resourceIdsresourceIdsresourceIds")
    if (!resourceIds || typeof resourceIds !== 'string') return false;
    
    // 将逗号分隔的章节ID转为数组并检查是否包含当前章节ID
    const chapterIdsArray = resourceIds.split(',');
    return chapterIdsArray
  })
  //获取知识点视频列表
  const getVideoList = () => {
    console.log(resourceIds.value,"resourceIdsresourceIds")
    const { chapterId, subActive } = state
    const subject = subjectList[subActive].key
    // console.log(chapterId,"chapterIdchapterId")
    const param = {
      subject,
      chapterId,
      videoIdList: query?.videoIds?query?.videoIds:''
      // videoIdList: resourceIds.value ? resourceIds.value : []
    }
    getVideoListApi(param)
      .then((res : any) => {
        const res2 = res.data
        if (res2?.length) {
          for (const i of res2) {
            let num = 1
            for (const x of i.knowledgeList) {
              x.num = num
              num++
              if (x.exampleVideoInfos) {
                //例题
                const name = x.videoInfos[0]?.videoName || ''
                for (const y of x.exampleVideoInfos) {
                  //知识点(例题)-收藏用
                  y['videoName3'] = `${name}(${y.videoName})`
                }
              }
            }
          }
          setData({
            data: res2,
            isShow: 1,
            subject
          })
        } else {
          setData({
            title: '',
            data: [],
            videoId: '',
            isShow: 1
          })
        }
      })
  }

  //我的收藏
  const goCollect = () => {
    const { subName, subActive } = state
    router.push({ name: "TeachRoomTeachCollect", query: { subName, subKey: subActive } })
  }

  // 知识点-跳转视频
  const wekePlay = (e : any) => {
    if (getIsMember()) {
      const { i, i2, i3 } = e.currentTarget.dataset
      const { data, subActive, chapterId } = state
      let info2 = data[i].knowledgeList[i2]
      const info = info2.videoInfos[i3]
      const { type } = info2
      const { videoId } = info
      router.push({ name: "TeachRoomTeachVideo", query: { id: chapterId, vid: videoId, subKey: subActive, type } })
    }
  }
  // 知识点例题-跳转视频
  const wekePlay2 = (e : any) => {
    if (getIsMember()) {
      const { i, i2, i3 } = e.currentTarget.dataset
      const { data, subActive, chapterId } = state
      let info2 = data[i].knowledgeList[i2]
      const info = info2.exampleVideoInfos[i3]
      const { type } = info2
      const { videoId } = info
      router.push({ name: "TeachRoomTeachVideo", query: { id: chapterId, vid: videoId, subKey: subActive, type } })
    }
  }

  //概述跳转h5
  const gaisuShow = (e : any) => {
    if (getIsMember()) {
      const { i, i2 } = e.currentTarget.dataset
      const { data, subject } = state
      const url = data[i].knowledgeList[i2].summary || ''
      if (url) {
        if (url.indexOf('https://') == 0) {
          router.push({ name: "TeachRoomTeachView", query: { url, subject } })
        } else {
          //html代码
          localStorage.explainStr = url
          router.push({ name: "TeachRoomTeachView", query: { subject } })
        }
      } else {
        //无知识点
        localStorage.explainStr = ''
        router.push({ name: "TeachRoomTeachView", query: { subject } })
      }
    }
  }

  // 点击科目
  const setSubject = () => {
    const { subActive, subList } = state
    let key = '', text = ''
    
    // 匹配选中 - 优化匹配逻辑
    const matchedItem = subList.find(item => {
      // 精确匹配
      if (item.key === subActive) return true;
      
      // 前缀匹配
      if (item.key.startsWith(subActive) || 
          (typeof subActive === 'string' && subActive.startsWith(item.key))) {
        return true;
      }
      
      // 包含匹配
      if (item.key.includes(subActive)) return true;
      
      return false;
    });
    
    if (matchedItem) {
      key = matchedItem.key;
      text = matchedItem.text;
      
      // 如果找到匹配项但不是精确匹配，更新subActive为完整key
      if (key !== subActive) {
        state.subActive = key;
      }
    }
    setData({
      subName: text,
      isRefresh: 1
    })
    router.replace({ name: "TeachRoomTeachList", query: { subName: text } })
    // return
    // getBookVersion()
    // //列表置顶
    // setTop()
  }
  // 设置tabs科目枚举
  const setSubList = () => {
    const { sortArr, sortArr2, learnNow, isFirst } = state
    const { gradeId } = learnNow
    if ([1, 2, 3, 4, 5, 6].includes(gradeId)) {
      // 小学
      const list = subjectEnum2
      //学科英文匹配数字key
      for (const i of list) {
        i['key2'] = subjectList[i.key].key
      }
      //学科排序
      const list2 : any = []
      for (const x of sortArr2) {
        for (const y of list) {
          if (x == y.text) {
            list2.push(y)
          }
        }
      }
      setData({
        subList: list2,
        subActive: 'chinese3'
      })
      //匹配页面传值的学科
      let name = state.subName
      if (isFirst && name) {
        for (let i of list2) {
          if (i.text == name) {
            setData({
              subActive: query?.subActive ||i.key,
              isFirst: 0
            })
            break
          }
        }
      }
    } else {
      // 高中或初中（7年级无化学）
      const isJunior = [7, 8, 9].includes(gradeId)
      const cList = deepClone(subjectEnum)
      //追加初中科学
      cList.push({
        key: 'science',
        text: '科学'
      })
      const list = cList.map((item : any) => {
        item.key = item.key + (isJunior ? '' : '2')
        return item
      })
      //学科英文匹配数字key
      for (const i of list) {
        i.key2 = subjectList[i.key].key
      }
      //学科排序
      const list2 : any = []
      for (const x of sortArr) {
        for (const y of list) {
          if (x == y.text) {
            if (!(gradeId == 7 && x == '化学')) {
              list2.push(y)
            }
          }
        }
      }
      setData({
        subList: list2,
        subActive: isJunior ? 'chinese' : 'chinese2'
      })
      //匹配页面传值的学科
      let name = state.subName
      if (isFirst && name) {
        for (let i of list2) {
          if (i.text == name) {
            setData({
              subActive: i.key,
              isFirst: 0
            })
            break
          }
        }
      }
    }
    getBookVersion()
  }
  // 查询指定教材版本，并设定默认学期的数据
  const getBookVersion = () => {
    const { learnNow, subActive, sessionObject, termNameNow, subObject } = state
    const { versions } = learnNow
    
    // 优化科目匹配逻辑
    const user : any = versions.find((item : any) => {
      // 精确匹配
      if (item.subject === subActive) return true;
      
      // 包含匹配
      if (item.subject.includes(subActive)) return true;
      
      // 前缀匹配
      if (typeof item.subject === 'string' && 
         (item.subject.startsWith(subActive) || 
          (typeof subActive === 'string' && subActive.startsWith(item.subject)))) {
        return true;
      }
      
      return false;
    })
    
    // 检查 user 是否存在，避免访问 undefined 的属性  todo zzh
    if (!user) {
      // console.log(user,"打印一下 进行数 user 715")
      ElMessage.error("当前年级没有该科目教材，请更换年级再来吧！")
      setData({
        isShowContent: false
      })
      return
    }
    
    //记录学习学科
    const subName = user.subjectName
    setLearnKey(subName)
    setData({
      subName
    })
    const isReq = !sessionObject[subActive]
    if (isReq) {
      // 没有请求过数据
      const obj = deepClone(sessionObject)
      const obj2 = deepClone(subObject)
      if (user) {
        const list : any = []
        list.push(user)
        if (!list.length) {
          // console.log(user,"打印一下 进行数 list.length 738")
          ElMessage.error('当前年级没有该科目教材，请更换年级再来吧！')
          setData({
            isShowContent: false
          })
          return
        }
        const subjectNow = list.find((item : any) => {
          if (!item.termName) {
            //null改成全年制
            return true
          } else if (item.termName != termNameNow) {
            //如果上下学期不一致
            return item
          } else {
            return item.termName === termNameNow
          }
        })
        obj[subActive] = list
        obj2[subActive] = subjectNow
        console.log("obj2---",obj2)
        setData({
          sessionObject: obj,
          subObject: obj2,
          isShowContent: true
        })
        setBookName()
      } else {
        console.log(user,"打印一下 进行数 765")
        ElMessage.error("当前年级没有该科目教材，请更换年级再来吧！")
        setData({
          isShowContent: false
        })
      }
    } else {
      setBookName()
    }
  }
  //设置当前教材
  const setBookName = () => {
    const { subObject, subActive, learnNow } = state
    const { gradeId, gradeName } = learnNow
    const { editionName, name, typeName, termName } = subObject[subActive]
    let bookName = ''
    //小学初中显示上下学期
    const bookName1 = editionName + (typeName || '') + gradeName + (termName || '全年制')
    //高中用name,去除教材同名为空
    const bookName2 = editionName + gradeName + (name?.replace(editionName, '') || '')
    if (gradeId > 9) {
      bookName = bookName2
    } else {
      bookName = bookName1
    }
    setData({
      bookName
    })
    getCourseList()
  }
</script>

<style lang="scss" scoped>
  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    overflow-y: auto;
    background: #F5F5F5;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
  }

  /* 章节 */
  .menu_lt {
    float: left;
    width: 17.75rem;
    height: calc(100vh - 7.5rem);
    overflow-y: auto;
    border: .0625rem solid #eaeaea;
    border-bottom: 0;
    background: #ffffff;
    display: flex;
    flex-flow: column;
  }

  .subbox {
    float: left;
    width: 100%;
    box-sizing: border-box;
    padding: 1.25rem .875rem;
  }

  .subkey {
    width: 100%;
  }

  .subkey :deep(.el-select__wrapper) {
    height: 2.0625rem;
    border-radius: 1.375rem;
    background: #ffffff;
    box-shadow: 0 0 0 .0625rem #00c9a3 inset;
    font-size: 1rem;
  }

  .subkey :deep(.el-select__selection) {
    position: relative;
    top: -0.0625rem;
  }

  :deep(.subkey .el-select__icon svg) {
    display: none;
  }

  :deep(.subkey .el-select__icon) {
    background: url(@/assets/img/teachroom/down2.svg) no-repeat;
    background-size: .875rem .5625rem;
    background-position: center center;
  }

  /* select */
  .el-select-dropdown__item.selected {
    color: #00C9A3 !important;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover,
  .el-select-dropdown__item.is-hovering {
    color: #00C9A3 !important;
    background: #e5f9f6 !important;
  }

  .el-select-dropdown {
    box-shadow: 0 .125rem 1.25rem 0 #00000040 !important;
  }

  /* 任务标签样式 */
  .task-badge {
    position: absolute;
    left: -30px;
    top: -8px;
    width: 38px;
    height: 24px;
    z-index: 1;
    background: url('@/assets/img/synchronous/task-badge.png')center center no-repeat;
    background-size: 100%;
  }

  .el-select-dropdown__item {
    height: 3.0625rem !important;
    line-height: 3.0625rem !important;
  }

  .menu_ul {
    flex: 1;
    overflow-y: auto;
    padding: 0 .875rem;
  }

  .menu_ul div {
    float: left;
  }

  .menu_li {
    width: 100%;
  }

  .menu1 {
    width: 100%;
    line-height: 2.5625rem;
    border-left: .1875rem solid #f5f5f5;
    background: #f5f5f5;
    margin: 0 0 1.125rem;
    display: flex;
    align-items: center;
  }

  .menu1.active {
    border-left: .1875rem solid #00c9a3;
  }

  .menu1 div {
    flex: 1;
    line-height: 2.5625rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 0 0 0 .4375rem;
  }

  .menu_img {
    float: right;
    width: .875rem;
    height: .8125rem;
    margin: 0 .625rem 0 0;
  }

  .menu_img.up {
    transform: rotate(-180deg);
  }

  .child {
    width: 100%;
  }

  .menu2 {
    position: relative;
    width: 100%;
    margin: 0 0 1.125rem;
    display: flex;
    align-items: center;
  }

  .menu2 div {
    flex: 1;
    line-height: 2.5625rem;
    color: #666;
    font-size: 1rem;
    margin: 0 0 0 1.875rem;
  }

  .menu2.active {
    background: #e5f9f6;
  }

  .menu2.active div {
    color: #009c7f;
  }

  .menu3 {
    width: 100%;
  }

  .menu3 div {
    width: 100%;
    line-height: 2.5625rem;
    color: #666;
    font-size: 1rem;
    box-sizing: border-box;
    padding: 0 .3125rem 0 3.125rem;
    // margin: 0 0 1.125rem;
  }

  .menu3 div.active {
    color: #009c7f;
    background: #e5f9f6;
  }

  .menu1:hover,
  .menu2:hover,
  .menu3 div:hover {
    cursor: pointer;
  }

  /* 当前教材 */
  .menu_rt {
    float: right;
    width: 62.5rem;
    height: calc(100vh - 7.5rem);
    overflow-y: auto;
    margin: 0 0 0 .625rem;
    display: flex;
    flex-flow: column;
  }

  .book {
    width: 100%;
    height: 5rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
    box-sizing: border-box;
    padding: 1.375rem 1.5625rem;
  }

  .book_ver {
    float: left;
    line-height: 2.25rem;
    color: #2a2b2a;
    font-size: 1rem;
  }

  .book_ver span {
    font-weight: bold;
  }

  .book_fav {
    float: right;
    width: 6.875rem;
    height: 2.25rem;
    border-radius: .25rem;
    border: .0625rem solid #009c7f;
    color: #009c7f;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .book_fav img {
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 0;
  }

  /* 知识点 */
  .wk_box {
    width: 100%;
    .wk_ul{
      display: flex;
      flex-wrap: wrap;
    }
  }

  .wk_h1 {
    width: 57.625rem;
    box-sizing: border-box;
    padding: 1.25rem 0 .625rem;
    line-height: 1.3125rem;
    color: #009c7f;
    font-size: 1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    border-bottom: .0625rem dashed #eaeaea;
  }

  .wk_h1 span:first-child {
    float: left;
    width: .875rem;
    height: 1rem;
    border-radius: 0 .375rem .375rem 0;
    background: #5a85ec;
    margin: 0 .6875rem 0 0;
  }

  .wk_ul {
    width: 100%;
    margin: .625rem 0 0;
  }

  .wk_li {
    width: 24.2%;
    border-radius: .625rem;
    // overflow: hidden;
    margin: 0 1% 1.25rem 0;
    .task-badge{
      position: absolute;
      left: 0px;
      top: -20px;
      width: 38px;
      height: 24px;
      z-index: 1;
    }
  }

  .wk_li:nth-child(4n+4) {
    margin-right: 0;
  }

  .wk_pic,
  .wk_img {
    position: relative;
    float: left;
    width: 100%;
    height: 8.5625rem;
  }

  .wk_play {
    float: right;
    width: 1.25rem;
    height: 1.25rem;
    margin: -7.9375rem .625rem 0 0;
    position: relative;
    z-index: 8;
  }

  .wk_bom {
    width: 100%;
    box-sizing: border-box;
    padding: .625rem;
    background: #fff;
  }

  .wk_tit {
    width: 100%;
    line-height: 1.1875rem;
    color: #2a2b2a;
    font-size: .875rem;
  }

  .wk_state {
    width: 100%;
    margin: 1rem 0 0;
    display: flex;
    justify-content: space-between;
  }

  .wk_point {
    width: 100%;
    line-height: 2rem;
    text-align: center;
    border-radius: .25rem;
    background: #e5f9f6;
    color: #009c7f;
    font-size: .875rem;
    margin: .625rem 0 0;
  }

  .wk_status {
    width: 3.75rem;
    height: 1.5rem;
    line-height: 1.5rem;
    text-align: center;
    border-radius: 0 .75rem .75rem 0;
    color: #ffffff;
    font-size: .75rem;
    position: relative;
    left: -0.625rem;
  }

  .status0 {
    background: #999999;
  }

  .status1 {
    background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
  }

  .status2 {
    background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
  }

  div.wk_collect {
    float: right;
    margin: 0 1.875rem 0 0;
  }

  .wk_collect img {
    width: 1.125rem;
    height: 1.125rem;
  }

  .wk_collect img:first-child,
  .wk_collect.active img:last-child {
    display: inline-block;
  }

  .wk_collect img:last-child,
  .wk_collect.active img:first-child {
    display: none;
  }

  div.wk_thumbs {
    line-height: 1.125rem;
    color: #666666;
    font-size: .75rem;
    float: right;
  }

  .wk_thumbs img {
    float: left;
    width: 1.125rem;
    height: 1.125rem;
    margin: 0 .375rem 0 0;
  }

  .wk_thumbs img:nth-child(1),
  .wk_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .wk_thumbs img:nth-child(2),
  .wk_thumbs.active img:nth-child(1) {
    display: none;
  }

  .book_fav:hover,
  .wk_pic:hover,
  .wk_play:hover,
  .wk_tit:hover,
  .wk_thumbs img:hover,
  .wk_collect:hover,
  .wk_point:hover {
    cursor: pointer;
  }

  /* 暂无数据 */
  .nodata {
    flex: 1;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
  }

  .nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }
</style>
